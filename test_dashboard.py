#!/usr/bin/env python3
"""
Playwright 测试控制台 - Web 界面
"""
import streamlit as st
import os
import subprocess
import glob
import webbrowser
from datetime import datetime


def get_test_files():
    """获取所有测试文件"""
    test_files = []
    for pattern in ["tests/**/*.py", "tests/*.py"]:
        files = glob.glob(pattern, recursive=True)
        for file in files:
            if not file.endswith("__init__.py") and not file.endswith("conftest.py"):
                test_files.append(file.replace("\\", "/"))
    return sorted(test_files)


def get_test_methods(file_path):
    """获取测试文件中的测试方法"""
    methods = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            current_class = None
            
            for line in lines:
                line = line.strip()
                if line.startswith('class ') and 'Test' in line:
                    current_class = line.split('class ')[1].split('(')[0].split(':')[0]
                elif line.startswith('def test_'):
                    method_name = line.split('def ')[1].split('(')[0]
                    if current_class:
                        methods.append(f"{current_class}::{method_name}")
                    else:
                        methods.append(method_name)
    except Exception as e:
        st.error(f"读取测试文件失败: {e}")
    
    return methods


def open_report_in_browser(report_path):
    """使用系统默认浏览器打开HTML报告"""
    try:
        # 将报告路径转换为绝对路径
        abs_path = os.path.abspath(report_path)

        # 使用webbrowser模块打开文件
        webbrowser.open(f'file://{abs_path}')

    except Exception as e:
        st.error(f"无法打开报告: {e}")
        st.code(f"请手动打开报告文件: {os.path.abspath(report_path)}")


def run_test(test_path, browser, browser_channel, headless, parallel, reruns, markers):
    """执行测试"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 构建命令
    cmd = ["pytest", test_path]

    # 浏览器配置
    if browser:
        cmd.extend(["--browser", browser])
    if browser_channel:
        cmd.extend(["--browser-channel", browser_channel])

    # 显示模式配置 - 通过环境变量控制 Playwright
    env = os.environ.copy()
    if headless:
        env["PLAYWRIGHT_HEADLESS"] = "true"  # 设置环境变量控制无头模式
    else:
        env["PLAYWRIGHT_HEADLESS"] = "false"  # 设置环境变量控制有头模式

    # 并行执行
    if parallel:
        cmd.extend(["-n", "auto"])

    # 失败重试
    if reruns > 0:
        cmd.extend(["--reruns", str(reruns)])

    # 测试标记
    if markers:
        cmd.extend(["-m", markers])

    # 报告和输出
    cmd.extend([
        f"--html=reports/test_report_{timestamp}.html",
        "--self-contained-html",
        "-v", "-s", "--tb=short"
    ])

    return cmd, timestamp, env


def run_codegen(url=None):
    """启动 Playwright Codegen"""
    try:
        # 构建 playwright codegen 命令
        cmd = ["playwright", "codegen"]

        # 如果提供了 URL，添加到命令中
        if url and url.strip():
            cmd.append(url.strip())

        # 启动 codegen（非阻塞方式）
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )

        return True, process, cmd

    except FileNotFoundError:
        return False, None, ["playwright 未找到，请确保已安装 Playwright"]
    except Exception as e:
        return False, None, [f"启动失败: {str(e)}"]


def main():
    """主界面"""
    st.set_page_config(
        page_title="Playwright 测试控制台",
        page_icon="🎭",
        layout="wide"
    )
    
    st.title("🎭 Playwright 测试控制台")
    st.markdown("---")
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 测试配置")
        
        # 浏览器配置
        st.subheader("🌐 浏览器设置")
        browser = st.selectbox(
            "浏览器引擎",
            ["chromium", "firefox", "webkit"],
            index=0
        )
        
        browser_channel = st.selectbox(
            "浏览器版本",
            ["", "chrome", "chrome-dev", "chrome-beta", "msedge", "msedge-dev"],
            index=1 if browser == "chromium" else 0
        )
        
        headless = st.checkbox("无头模式", value=False)
        
        # 执行配置
        st.subheader("🚀 执行设置")
        parallel = st.checkbox("并行执行", value=False)
        reruns = st.number_input("失败重试次数", min_value=0, max_value=5, value=0)
        
        # 测试标记
        st.subheader("🏷️ 测试标记")
        markers = st.selectbox(
            "选择标记",
            ["", "web", "api", "smoke", "regression"],
            index=0
        )

        # 代码生成工具
        st.subheader("🎯 代码生成工具")
        codegen_url = st.text_input(
            "目标网址 (可选)",
            placeholder="https://example.com",
            help="留空将启动空白页面的 Codegen"
        )

        if st.button("🚀 启动 Codegen", use_container_width=True):
            success, process, cmd = run_codegen(codegen_url)

            if success:
                st.success("✅ Playwright Codegen 已启动!")
                st.code(" ".join(cmd))


            else:
                st.error("❌ 启动 Codegen 失败!")
                st.error(" ".join(cmd))
    
    # 主内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📁 选择测试文件")
        
        # 获取测试文件
        test_files = get_test_files()
        
        if not test_files:
            st.error("未找到测试文件！")
            return
        
        # 选择测试文件
        selected_file = st.selectbox(
            "测试文件",
            [""] + test_files,
            index=0
        )
        
        if selected_file:
            # 获取测试方法
            test_methods = get_test_methods(selected_file)
            
            if test_methods:
                st.subheader("🎯 选择测试方法")
                selected_method = st.selectbox(
                    "测试方法 (可选，留空执行整个文件)",
                    [""] + test_methods,
                    index=0
                )
                
                # 构建测试路径
                if selected_method:
                    test_path = f"{selected_file}::{selected_method}"
                else:
                    test_path = selected_file
            else:
                test_path = selected_file
                selected_method = None
        else:
            test_path = None
            selected_method = None
    
    with col2:
        st.header("📊 测试报告")

        # 初始化session state
        if 'show_reports' not in st.session_state:
            st.session_state.show_reports = False

        # 切换报告显示的按钮
        if st.session_state.show_reports:
            if st.button("🔼 隐藏报告", use_container_width=True):
                st.session_state.show_reports = False
                st.rerun()
        else:
            if st.button("📋 查看最近报告", use_container_width=True):
                st.session_state.show_reports = True
                st.rerun()

        # 只有当用户选择显示时才获取和显示报告
        if st.session_state.show_reports:
            report_files = glob.glob("reports/*.html")
            if report_files:
                latest_reports = sorted(report_files, key=os.path.getmtime, reverse=True)[:5]

                st.subheader("📋 最近报告")
                for report in latest_reports:
                    report_name = os.path.basename(report)
                    report_time = datetime.fromtimestamp(os.path.getmtime(report)).strftime("%Y-%m-%d %H:%M:%S")

                    if st.button(f"📄 {report_name}", key=report):
                        # 使用系统默认浏览器打开报告
                        open_report_in_browser(report)

                    st.caption(f"创建时间: {report_time}")
            else:
                st.info("暂无测试报告")

        # 清理报告按钮
        if st.button("🧹 清理报告", use_container_width=True):
            try:
                report_files = glob.glob("reports/*.html") + glob.glob("reports/*.png")
                for file in report_files:
                    os.remove(file)
                st.success(f"🗑️ 已清理 {len(report_files)} 个报告文件")
                # 清理后如果正在显示报告，刷新显示
                if st.session_state.show_reports:
                    st.rerun()
            except Exception as e:
                st.error(f"清理失败: {e}")
    
    # 执行按钮
    st.markdown("---")
    
    if test_path:
        if st.button("🚀 执行测试", type="primary", use_container_width=True):
            # 确保报告目录存在
            os.makedirs("reports", exist_ok=True)

            # 构建命令
            cmd, timestamp, env = run_test(
                test_path, browser, browser_channel,
                headless, parallel, reruns, markers
            )

            # 显示命令
            st.code(" ".join(cmd))

            # 执行测试
            with st.spinner("正在执行测试..."):
                try:
                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        encoding='utf-8',
                        timeout=300,
                        cwd=os.getcwd(),
                        env=env
                    )

                    # 显示结果
                    if result.returncode == 0:
                        st.success("✅ 测试执行成功!")
                    else:
                        st.error("❌ 测试执行失败!")

                    # 显示输出
                    with st.expander("📄 详细输出"):
                        st.text(result.stdout)
                        if result.stderr:
                            st.text("错误信息:")
                            st.text(result.stderr)

                    # 报告链接
                    report_file = f"reports/test_report_{timestamp}.html"
                    if os.path.exists(report_file):
                        st.success(f"📊 测试报告已生成: {report_file}")

                except subprocess.TimeoutExpired:
                    st.error("⏰ 测试执行超时!")
                except Exception as e:
                    st.error(f"💥 执行出错: {e}")
    else:
        st.warning("⚠️ 请先选择测试文件")


if __name__ == "__main__":
    main()
