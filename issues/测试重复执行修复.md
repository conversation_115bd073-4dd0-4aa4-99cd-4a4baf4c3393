# Playwright 测试重复执行问题修复

## 问题描述
用户发现 Playwright 测试会执行两次，显示为：
- `test_example[chromium0]` 
- `test_example[chromium1]`

## 问题分析
通过深入调试发现问题根源是 **pytest.ini 配置与命令行参数重复**：

1. **配置重复**：pytest.ini 中配置了 `--browser chromium --browser-channel chrome`
2. **命令行重复**：Dashboard 或手动命令又指定了相同的浏览器参数
3. **插件行为**：Playwright pytest 插件认为需要运行两个不同的浏览器配置
4. **参数化结果**：创建了 `test_example[chromium0]` 和 `test_example[chromium1]` 两个实例

## 解决方案
从 `pytest.ini` 配置文件中移除浏览器相关参数，只通过命令行或 Dashboard 指定浏览器配置。

## 修复详情

### 修改前的 pytest.ini：
```ini
[pytest]
testpaths = tests
addopts =
    --browser chromium
    --browser-channel chrome
    --html=reports/report.html
    --self-contained-html
    --tb=short
    -v
```

### 修改后的 pytest.ini：
```ini
[pytest]
testpaths = tests
addopts =
    --html=reports/report.html
    --self-contained-html
    --tb=short
    -v
```

## 验证结果

### 修复前：
```
test_example[chromium0] PASSED
test_example[chromium1] PASSED
2 passed
```

### 修复后：
```
test_example[chromium] PASSED
1 passed
```

## 技术说明

1. **配置分离**：pytest.ini 只包含通用配置，浏览器配置通过命令行指定
2. **避免重复**：防止配置文件和命令行参数冲突
3. **灵活性**：Dashboard 可以自由选择不同浏览器而不会产生冲突
4. **兼容性**：保持与现有 Dashboard 功能的完全兼容

## 完成时间
2025-07-30

## 状态
✅ 已完成并验证
