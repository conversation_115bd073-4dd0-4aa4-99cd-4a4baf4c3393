# Web UI报告界面优化

## 任务背景
用户希望调整web UI界面：
1. 最近报告需要点击一下才出现（而不是直接显示）
2. 清理报告按钮应该和报告相关的地方在一起

## 问题分析
通过代码检查发现：
1. test_dashboard.py第194-213行，最近报告是直接显示的
2. 清理报告按钮在第272行，位于执行测试按钮旁边
3. 用户希望界面更加整洁，报告相关功能集中管理

## 解决方案
采用streamlit expander组件实现可折叠的最近报告，并将清理功能移至报告区域

### 具体实现
1. **最近报告按需加载**
   - 使用session state控制报告显示状态
   - 添加切换按钮："📋 查看最近报告" / "🔼 隐藏报告"
   - 只有用户点击查看时才实时获取报告文件
   - 真正的按需加载，性能更优

2. **清理报告按钮重新定位**
   - 将清理报告按钮从执行区域移动到报告区域
   - 位于expander下方，逻辑更加合理
   - 保持原有的清理功能不变

3. **执行区域布局调整**
   - 移除原有的清理报告按钮
   - 使用`st.empty()`占位符保持布局

## 修改内容

### 1. 报告区域修改（第193-240行）
```python
with col2:
    st.header("📊 测试报告")

    # 初始化session state
    if 'show_reports' not in st.session_state:
        st.session_state.show_reports = False

    # 切换报告显示的按钮
    if st.session_state.show_reports:
        if st.button("🔼 隐藏报告", use_container_width=True):
            st.session_state.show_reports = False
            st.rerun()
    else:
        if st.button("📋 查看最近报告", use_container_width=True):
            st.session_state.show_reports = True
            st.rerun()

    # 只有当用户选择显示时才获取和显示报告
    if st.session_state.show_reports:
        report_files = glob.glob("reports/*.html")
        if report_files:
            latest_reports = sorted(report_files, key=os.path.getmtime, reverse=True)[:5]

            st.subheader("📋 最近报告")
            for report in latest_reports:
                report_name = os.path.basename(report)
                report_time = datetime.fromtimestamp(os.path.getmtime(report)).strftime("%Y-%m-%d %H:%M:%S")

                if st.button(f"📄 {report_name}", key=report):
                    # 使用系统默认浏览器打开报告
                    open_report_in_browser(report)

                st.caption(f"创建时间: {report_time}")
        else:
            st.info("暂无测试报告")

    # 清理报告按钮
    if st.button("🧹 清理报告", use_container_width=True):
        try:
            report_files = glob.glob("reports/*.html") + glob.glob("reports/*.png")
            for file in report_files:
                os.remove(file)
            st.success(f"🗑️ 已清理 {len(report_files)} 个报告文件")
            # 清理后如果正在显示报告，刷新显示
            if st.session_state.show_reports:
                st.rerun()
        except Exception as e:
            st.error(f"清理失败: {e}")
```

### 2. 执行区域修改（第281-283行）
```python
with col2:
    st.empty()  # 占位符，保持布局
```

## 预期效果
1. 用户需要点击"查看最近报告"才能看到报告列表，真正的按需加载
2. 报告文件只在用户需要时才获取，提升页面加载性能
3. 清理报告按钮位于报告区域，功能逻辑更加合理
4. 清理报告后自动刷新显示，用户体验更好
5. 界面更加整洁，减少视觉干扰

## 测试验证
启动Streamlit应用测试修改效果：
```bash
python start_dashboard.py
# 访问 http://localhost:8501
```

验证要点：
- 最近报告默认折叠，点击后展开
- 清理报告按钮在报告区域正常工作
- 报告打开功能正常
- 执行测试功能不受影响
