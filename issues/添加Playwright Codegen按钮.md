# 添加 Playwright Codegen 按钮

## 任务背景
用户希望在 UI 中添加一个按钮来调用 playwright codegen，方便录制和生成测试代码。

## 需求分析
1. 需要 URL 输入框（可选）
2. 使用默认浏览器
3. 按钮位置由开发者决定

## 解决方案
在侧边栏添加 "🎯 代码生成工具" 区域，包含：
- URL 输入框（可选）
- 启动 Codegen 按钮
- 使用说明和提示信息

## 实现细节

### 1. 新增 run_codegen 函数
**位置**: test_dashboard.py 第105-129行
**功能**: 
- 构建 playwright codegen 命令
- 支持可选的 URL 参数
- 非阻塞方式启动进程
- 错误处理和异常捕获

```python
def run_codegen(url=None):
    """启动 Playwright Codegen"""
    try:
        # 构建 playwright codegen 命令
        cmd = ["playwright", "codegen"]
        
        # 如果提供了 URL，添加到命令中
        if url and url.strip():
            cmd.append(url.strip())
        
        # 启动 codegen（非阻塞方式）
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        return True, process, cmd
        
    except FileNotFoundError:
        return False, None, ["playwright 未找到，请确保已安装 Playwright"]
    except Exception as e:
        return False, None, [f"启动失败: {str(e)}"]
```

### 2. 侧边栏添加代码生成工具区域
**位置**: test_dashboard.py 第177-206行
**内容**:
- URL 输入框（带占位符和帮助文本）
- 启动 Codegen 按钮
- 成功/失败状态显示
- 使用说明折叠面板

```python
# 代码生成工具
st.subheader("🎯 代码生成工具")
codegen_url = st.text_input(
    "目标网址 (可选)",
    placeholder="https://example.com",
    help="留空将启动空白页面的 Codegen"
)

if st.button("🚀 启动 Codegen", use_container_width=True):
    success, process, cmd = run_codegen(codegen_url)
    
    if success:
        st.success("✅ Playwright Codegen 已启动!")
        st.info("💡 Codegen 窗口已在新窗口中打开，请在浏览器中操作录制代码")
        st.code(" ".join(cmd))
        
        # 显示提示信息
        with st.expander("📖 使用说明"):
            st.markdown("""
            **Playwright Codegen 使用指南：**
            
            1. 🌐 **浏览器窗口**：在打开的浏览器中进行操作
            2. 📝 **代码窗口**：查看自动生成的测试代码
            3. 🎯 **录制操作**：点击、输入、导航等操作会自动转换为代码
            4. 📋 **复制代码**：从代码窗口复制生成的代码到您的测试文件
            5. 🔍 **选择器**：使用检查器选择页面元素
            6. ⏹️ **停止录制**：关闭窗口或按 Ctrl+C 停止
            """)
    else:
        st.error("❌ 启动 Codegen 失败!")
        st.error(" ".join(cmd))
```

## 功能特性
1. **可选 URL 输入**：用户可以输入目标网址，也可以留空启动空白页面
2. **友好的用户界面**：清晰的按钮和状态提示
3. **详细的使用说明**：帮助用户了解如何使用 Codegen
4. **错误处理**：处理 Playwright 未安装等异常情况
5. **非阻塞启动**：不会阻塞 Streamlit 界面

## 预期效果
1. 用户点击按钮后，Playwright Codegen 在新窗口中启动
2. 如果输入了 URL，会直接导航到该页面
3. 用户可以在浏览器中操作，实时生成测试代码
4. 界面显示启动状态和使用指南

## 测试验证
启动 Streamlit 应用测试新功能：
```bash
python start_dashboard.py
# 访问 http://localhost:8501
```

验证要点：
- 侧边栏显示代码生成工具区域
- URL 输入框正常工作
- 启动 Codegen 按钮功能正常
- 错误处理机制有效
- 使用说明显示正确

## 完成时间
2025-07-31
